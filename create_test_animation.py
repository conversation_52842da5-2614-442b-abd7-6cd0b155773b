#!/usr/bin/env python3
"""Create a test animation to verify wealth numbers functionality."""

import pandas as pd
import numpy as np
from src.animation.time_series_animation import create_wealth_animation

def create_test_animation():
    """Create a test animation with wealth numbers."""
    print("Creating test animation...")
    
    # Create sample data for a 1-year period with monthly data
    dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='M')
    n_periods = len(dates)
    
    # Set random seed for reproducible results
    np.random.seed(123)
    
    # Create realistic investment scenario
    monthly_investment = 1000
    base_investment = np.cumsum([monthly_investment] * n_periods)
    
    # Simulate different stock performances with clear differences
    # AAPL: Strong performer
    aapl_returns = np.random.normal(0.02, 0.05, n_periods)  # 2% monthly avg
    aapl_multiplier = np.cumprod(1 + aapl_returns)
    
    # MSFT: Moderate performer  
    msft_returns = np.random.normal(0.01, 0.04, n_periods)  # 1% monthly avg
    msft_multiplier = np.cumprod(1 + msft_returns)
    
    # Create wealth data with clear final values
    wealth_data = {
        'Total Investments': base_investment,
        'AAPL': base_investment * aapl_multiplier,
        'MSFT': base_investment * msft_multiplier,
    }
    
    wealth_df = pd.DataFrame(wealth_data, index=dates)
    
    # Print final values for verification
    print("\nFinal wealth values:")
    for col in wealth_df.columns:
        final_value = wealth_df[col].iloc[-1]
        print(f"  {col}: ${final_value:,.0f}")
    
    # Create animation with specific settings for testing
    create_wealth_animation(
        wealth_df=wealth_df,
        investment_years=1,
        filename="/home/<USER>/projects/python/reelstonks/demo_wealth_numbers.mp4",
        duration_sec=6,  # Short duration for quick testing
        fps=30,
        title="Test Animation - Wealth Numbers",
        music_filename="DISABLED"  # Disable music for testing
    )
    
    print("\n✅ Test animation created at: /home/<USER>/projects/python/reelstonks/demo_wealth_numbers.mp4")
    print("\nExpected behavior:")
    print("- Lines should draw first (main animation)")
    print("- After lines finish, wealth numbers should appear at the end of each line")
    print("- Numbers should match line colors")
    print("- Numbers should be formatted like $15.2k, $18.7k, etc.")

if __name__ == "__main__":
    create_test_animation()
