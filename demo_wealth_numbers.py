#!/usr/bin/env python3
"""Demo script to show the wealth numbers feature in animations."""

import pandas as pd
import numpy as np
from src.animation.time_series_animation import create_wealth_animation

def create_demo_animation():
    """Create a demo animation showing wealth numbers at the end of lines."""
    # Create sample data for a 2-year period
    dates = pd.date_range(start='2022-01-01', end='2024-01-01', freq='M')
    n_periods = len(dates)
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Create realistic investment scenario
    monthly_investment = 1000
    base_investment = np.cumsum([monthly_investment] * n_periods)
    
    # Simulate different stock performances
    # AAPL: Strong performer with some volatility
    aapl_returns = np.random.normal(0.015, 0.06, n_periods)  # 1.5% monthly avg, 6% volatility
    aapl_multiplier = np.cumprod(1 + aapl_returns)
    
    # MSFT: Steady performer
    msft_returns = np.random.normal(0.012, 0.04, n_periods)  # 1.2% monthly avg, 4% volatility  
    msft_multiplier = np.cumprod(1 + msft_returns)
    
    # TSLA: Volatile performer
    tsla_returns = np.random.normal(0.008, 0.12, n_periods)  # 0.8% monthly avg, 12% volatility
    tsla_multiplier = np.cumprod(1 + tsla_returns)
    
    # Create wealth data
    wealth_data = {
        'Total Investments': base_investment,
        'AAPL': base_investment * aapl_multiplier,
        'MSFT': base_investment * msft_multiplier, 
        'TSLA': base_investment * tsla_multiplier,
    }
    
    wealth_df = pd.DataFrame(wealth_data, index=dates)
    
    print("Creating demo animation with wealth numbers...")
    print(f"Final wealth values:")
    for col in wealth_df.columns:
        final_value = wealth_df[col].iloc[-1]
        print(f"  {col}: ${final_value:,.0f}")
    
    # Create animation
    create_wealth_animation(
        wealth_df=wealth_df,
        investment_years=2,
        filename="demo_wealth_numbers.mp4",
        duration_sec=8,
        fps=30,
        title="Demo: Wealth Numbers at Line Ends",
        music_filename="DISABLED"  # Disable music for demo
    )
    
    print("Demo animation created: demo_wealth_numbers.mp4")
    print("\nFeatures demonstrated:")
    print("✅ Wealth numbers appear at the end of each line")
    print("✅ Numbers update as time progresses")
    print("✅ Numbers use the same color as their respective lines")
    print("✅ Numbers are formatted using make_int_human_readable (e.g., 1.5k, 2.3mn)")

if __name__ == "__main__":
    create_demo_animation()
