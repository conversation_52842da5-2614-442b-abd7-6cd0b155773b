# 🤖 ReelStonks Interactive Telegram Bot

The Interactive Telegram Bot provides a complete strategy selection and adjustment system with 55 random investment scenarios. Users can quickly select fascinating investment battles, preview strategies, and make adjustments before creating animations.

## ✨ Key Features

- **🎲 55 Random Strategies** - From Tesla vs Apple to Bitcoin vs Gold battles
- **🎯 Random-First Flow** - Default timeout automatically selects random strategy
- **📋 Strategy Previews** - See full details before animation creation
- **🔧 Interactive Adjustments** - Add, adjust, or skip strategies
- **⚡ Timing Analysis** - Compare peak vs low vs regular investment timing
- **🌍 Global Coverage** - US, European, Chinese companies and markets
- **📱 TikTok Integration** - Auto-generated engaging descriptions

## 🚀 Quick Start

### 1. Setup Telegram Bot

```toml
# config.toml
[telegram]
token = "YOUR_BOT_TOKEN_HERE"
chat_id = "YOUR_CHAT_ID_HERE"
enabled = true
interactive_menu = true
interactive_timeout_minutes = 5
```

### 2. Run ReelStonks

```bash
python main.py
```

### 3. Interactive Flow

**Step 1: Random Strategy Question (First Priority)**

```
🎲 Random Strategy

Do you want to use a random investment strategy?

This will select a surprise strategy from our collection of
powerful investment scenarios featuring companies from around the world.

Default: Yes (random strategy)

👇 Click your choice:
[🎲 Yes - Surprise Me!] [🎯 No - I'll Choose] [🔧 Default (Yes)]
```

**If Yes → Random Strategy Selected**
**If No → Manual Strategy Selection Menu**

**Step 2: Strategy Preview & Adjustment**
```
🎲 Selected Strategy

**⚔️ Defense vs Tech Titans**

The ultimate showdown between old-world defense and new-world technology:
Rheinmetall vs Palantir over 8 years...

📊 Configuration:
• Years: 8
• Amount: $750
• Frequency: Quarterly
• Strategies: 2

Do you want to change something?

[✅ No - Use This] [⏭️ Skip - New Random] [🔧 Yes - Adjust]
```

**Step 3: Adjustment Options (if Yes)**
```
🔧 Adjustment Options

What would you like to do?

• Add: Add more investment strategies
• Adjust: Modify existing configuration
• Both: Add strategies and adjust config

[➕ Add] [🔧 Adjust] [🔄 Both] [🔧 Default (Both)]
```

## 🎲 Random Strategy Categories (55 Total)

### 🤖 **AI & Technology Battles**
- **Nvidia vs AMD** - AI Chip Supremacy Battle
- **Tesla vs Google** - Autonomous Driving Future
- **Meta vs Unity** - Virtual Reality Metaverse
- **IBM vs Google** - Quantum Computing Revolution
- **Microsoft vs C3.ai** - AI Software Revolution

### ₿ **Crypto vs Traditional Finance**
- **Bitcoin vs JPMorgan** - Crypto vs Wall Street
- **Ethereum vs Gold** - Digital vs Physical Store of Value
- **Coinbase vs MicroStrategy** - Crypto Infrastructure Wars

### 🌍 **Global Market Showdowns**
- **S&P 500 vs DAX** - Global Market Championship
- **China A-Shares vs S&P 500** - East vs West Economic Power
- **UBS vs ING** - European Banking Leaders

### ⚡ **Timing Demonstrations**
- **S&P 500** - Perfect vs Bad Timing (20 years)
- **Apple** - Peak vs Low vs Regular timing (15 years)
- **Tesla** - Volatility timing effects (10 years)
- **Bitcoin** - Extreme timing scenarios (8 years)
- **Nvidia** - AI revolution timing (6 years)

### 🛡️ **Defense & Innovation**
- **Rheinmetall vs Palantir** - Defense vs Tech Titans
- **Boeing vs Lockheed Martin** - New Space Race
- **CrowdStrike vs Palo Alto** - Digital Fortress Wars

### 🎮 **Entertainment & Media**
- **GameStop vs AMC** - Meme Stock Revolution
- **Netflix vs Disney** - Streaming Domination Battle
- **Activision vs EA** - Gaming Empire Clash

## 🔧 Adjustment System

### **Add Strategies**
- Use existing strategy builder to add more investments
- Merge new strategies with selected random strategy
- Build custom combinations

### **Adjust Configuration**
- **General Settings**: Years, amount, frequency
- **Individual Timing**: Change timing for each strategy
  - 📈 **Regular** - Standard dollar-cost averaging
  - ⬆️ **Peaks** - Buy at monthly highs (worst timing)
  - ⬇️ **Lows** - Buy at monthly lows (best timing)

### **File Management**
- **Used Folder**: `assets/options/random/used/YYYYMMDD/`
- **Skipped Folder**: `assets/options/random/skipped/YYYYMMDD/`
- **Date Organization**: Strategies organized by date for tracking

## ⚙️ Configuration

### **Telegram Setup**
```toml
[telegram]
enabled = true
interactive_menu = true
interactive_timeout_minutes = 5
token = "YOUR_BOT_TOKEN"
chat_id = "YOUR_CHAT_ID"
```

### **TikTok Integration**
```toml
[upload.tiktok]
enabled = false  # Set to true for auto-upload
```

### **Music Integration**
```toml
[song]
use_song = "epic-music.mp3"
_use = true  # Enable/disable music
```

## 🔧 How It Works

### **Flow Overview**
1. **Random Question** - First priority: "Use random strategy?"
2. **Strategy Selection** - Random selection from 55 options
3. **Strategy Preview** - Full details with configuration summary
4. **Adjustment Choice** - No/Skip/Yes options
5. **Adjustment Flow** - Add/Adjust/Both if user chooses Yes
6. **File Management** - Move files to used/skipped folders
7. **Animation Creation** - Generate video with selected strategy

### **Default Behavior**
- **Timeout → Random** - No response defaults to random strategy
- **Random-First** - Random question appears before all other options
- **Smart Fallback** - System gracefully handles all edge cases

## 🎯 Example Random Strategies

### **Timing Comparison Example**
```toml
# Option 51: Market Timing Showdown
display_text = "📈 Perfect Timing vs Bad Timing"
description = "Ultimate demonstration of timing's impact on wealth: S&P 500 at peaks vs lows over 20 years"

[general]
years_ago = 20
investment_amount = 1000.0
investment_kind = "monthly"

[strategies.SPY_peaks]
ticker = "SPY"
name = "S&P 500 (Peak Timing)"
timing = "peaks"

[strategies.SPY_lows]
ticker = "SPY"
name = "S&P 500 (Low Timing)"
timing = "lows"
```

### **Global Showdown Example**
```toml
# Option 21: Defense vs Tech Showdown
display_text = "⚔️ Defense vs Tech Titans"
description = "Rheinmetall vs Palantir: old-world defense vs new-world technology over 8 years"

[strategies.RHM_quarterly]
ticker = "RHM.DE"
name = "Rheinmetall AG"
timing = "regular"

[strategies.PLTR_quarterly]
ticker = "PLTR"
name = "Palantir Technologies"
timing = "regular"
```

## 🚀 Benefits

### **🎲 Discover Fascinating Scenarios**
- 55 curated investment battles from around the world
- AI vs traditional companies, crypto vs finance, timing demonstrations
- Global coverage: US, European, Chinese markets

### **⚡ Lightning Fast**
- Random strategy selected in seconds
- No manual configuration needed
- Timeout defaults to random for instant results

### **🔧 Full Control**
- Preview strategies before animation
- Adjust timing, amounts, frequencies
- Add custom strategies to random selections
- Skip strategies you don't like

### **📱 Mobile-Friendly**
- Complete control via Telegram
- Clickable buttons for easy selection
- Works from anywhere in the world

### **🎬 Social Media Ready**
- Auto-generated TikTok descriptions
- Optimized animations for social platforms
- Engaging narratives for each strategy

---

**🎲 Ready to discover your next fascinating investment scenario?**

*Run `python main.py` and let the random strategy system surprise you!*
