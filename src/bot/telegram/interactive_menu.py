"""Interactive Telegram menu system for ReelStonks strategy selection."""

import time
from datetime import datetime
from typing import Optional, Dict, Any
import random
import shutil
from pathlib import Path
from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager
from src.bot.telegram._build import Strategy<PERSON>uilder, ConfigBuilder, OptionLoader
from src.bot.telegram._build.config_builder import GeneralConfigBuilder
from src.utils.paths import move_file_to_used_folder

LOGGER = get_logger(__name__)


class InteractiveMenu:
    """
    Interactive Telegram menu system with 3-level hierarchy:
    1. Main menu (Default, Predefined, Custom, Quick Crypto)
    2. Predefined submenu (if selected)
    3. Custom submenu (if selected)
    """

    def __init__(self, telegram_manager: TelegramManager):
        """
        Initialize the interactive menu system.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance for sending messages.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_minutes = telegram_manager.interactive_timeout_minutes
        self.timeout_seconds = self.timeout_minutes * 60
        self.option_loader = OptionLoader()
        self.strategy_builder = StrategyBuilder(telegram_manager, timeout_seconds=300)
        self.general_config_builder = GeneralConfigBuilder(
            telegram_manager, timeout_seconds=300
        )
        self.config_builder = ConfigBuilder(
            telegram_manager,
            timeout_seconds=300,
            strategy_builder=self.strategy_builder,
        )

        LOGGER.info(
            f"🎛️ Interactive menu initialized with {self.timeout_minutes} minute timeout"
        )

    def _clear_old_messages(self):
        """Clear any old messages from the Telegram chat to avoid processing stale responses."""
        LOGGER.info("🧹 Clearing old messages...")

        # Get all pending updates and mark them as processed
        updates = self.telegram_manager._get_updates()
        if updates:
            LOGGER.info(f"🧹 Cleared {len(updates)} old messages")

    def show_menu_and_wait(
        self, base_config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Show the main interactive menu and handle user selection.

        Args:
            base_config (Dict[str, Any]): Base configuration for fallback.

        Returns:
            Optional[Dict[str, Any]]: Selected configuration or None for default.
        """
        LOGGER.info("🎛️ Starting interactive menu system")

        # Clear any old messages before starting the menu
        self._clear_old_messages()

        # Step 1: Ask if user wants to use a random strategy (highest priority)
        use_random = self._ask_use_random_strategy()
        if use_random:
            # User wants random strategy - select and handle with adjustment options
            return self._handle_random_strategy_with_adjustments()

        # Step 2: Ask if user wants to specify general setup
        general_config = self._ask_general_setup()

        # Create working config with general settings if provided
        working_config = dict(base_config)
        if general_config:
            working_config["general"] = general_config

        # Step 3: Show main strategy menu (without random option since it was already handled)
        main_choice = self._show_main_menu()

        if main_choice == 1:
            # Use current config (default + any general changes)
            LOGGER.info("🔧 User selected default configuration")
            return working_config if general_config else None

        elif main_choice == 2:
            # Use predefined strategies
            LOGGER.info("📋 User selected predefined strategies")
            strategy_config = self._handle_predefined_strategies()
            if strategy_config and general_config:
                # Merge general config with strategy config
                strategy_config["general"] = general_config
            return strategy_config

        elif main_choice == 3:
            # Specify strategies directly in chat
            LOGGER.info("🎨 User selected custom strategy building")
            strategy_config = self._handle_custom_strategies(working_config)
            if strategy_config and general_config:
                # Merge general config with strategy config
                strategy_config["general"] = general_config
            return strategy_config

        else:
            # Timeout or invalid choice - use current config
            LOGGER.info("⏰ No valid selection, using current configuration")
            return working_config if general_config else None

    def _ask_general_setup(self) -> Optional[Dict[str, Any]]:
        """Ask if user wants to specify general setup parameters."""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Yes - Specify Setup", "callback_data": "general_yes"},
                    {"text": "⏭️ No - Use Default", "callback_data": "general_no"},
                ],
                [{"text": "🔧 Default (No)", "callback_data": "general_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "🔧 **General Setup**\n\n"
            "Do you want to specify the general setup?\n\n"
            "This includes:\n"
            "• Years of historical data\n"
            "• Investment amount per period\n"
            "• Investment frequency\n\n"
            "**Default: No (use config.toml settings)**\n\n"
            "👇 **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_callback_response()

        if response == "general_yes":
            LOGGER.info("🔧 User chose to specify general setup")
            self.telegram_manager.send_message("🔧 Setting up general configuration...")
            return self.general_config_builder.build_general_config_interactive()
        else:
            LOGGER.info("🔧 User chose to use default general setup")
            self.telegram_manager.send_message(
                "✅ Using default general setup from config.toml"
            )
            return None

    def _show_main_menu(self) -> int:
        """Show the main menu and get user selection."""
        # Create inline keyboard with clickable cards
        keyboard = {
            "inline_keyboard": [
                [{"text": "🔧 Use Default Config", "callback_data": "menu_1"}],
                [{"text": "📊 Use Predefined Strategies", "callback_data": "menu_2"}],
                [{"text": "🎨 Specify Strategies Directly", "callback_data": "menu_3"}],
            ]
        }

        self.telegram_manager.send_message(
            "🎯 **ReelStonks Interactive Menu**\n\n"
            f"⏱️ You have **{self.timeout_minutes} minutes** to choose an option.\n"
            "If no selection is made, the default configuration will be used.\n\n"
            "📋 **Choose your option by clicking one of the cards below:**",
            reply_markup=keyboard,
        )

        # Small delay to ensure message is sent before polling starts
        time.sleep(2)

        return self._wait_for_main_menu_response()

    def _wait_for_main_menu_response(self) -> int:
        """Wait for main menu response (button clicks or text messages)."""
        start_time = time.time()
        menu_start_timestamp = int(start_time)  # Unix timestamp when menu was shown

        LOGGER.info(
            f"🕐 Waiting for user response (timeout: {self.timeout_minutes} minutes)"
        )

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                # Handle callback queries (button presses)
                if "callback_query" in update:
                    callback_query = update["callback_query"]
                    callback_data = callback_query.get("data", "")
                    callback_id = callback_query.get("id", "")

                    LOGGER.info(f"🎯 Received button press: '{callback_data}'")

                    if callback_data == "menu_1":
                        self.telegram_manager.answer_callback_query(
                            callback_id, "Using default configuration"
                        )
                        self.telegram_manager.send_message(
                            "✅ Using default configuration"
                        )
                        return 1
                    elif callback_data == "menu_2":
                        self.telegram_manager.answer_callback_query(
                            callback_id, "Selected predefined strategies"
                        )
                        self.telegram_manager.send_message(
                            "✅ Selected predefined strategies"
                        )
                        return 2
                    elif callback_data == "menu_3":
                        self.telegram_manager.answer_callback_query(
                            callback_id, "Selected custom strategy building"
                        )
                        self.telegram_manager.send_message(
                            "✅ Selected custom strategy building"
                        )
                        return 3
                    else:
                        self.telegram_manager.answer_callback_query(
                            callback_id, "Invalid selection"
                        )

                # Handle text messages (fallback)
                elif "message" in update and "text" in update["message"]:
                    # Only process messages sent after the menu was shown
                    message_timestamp = update["message"].get("date", 0)
                    if message_timestamp < menu_start_timestamp:
                        LOGGER.info(f"🕐 Ignoring old message from {message_timestamp}")
                        continue

                    text = update["message"]["text"].strip().lower()
                    LOGGER.info(f"📱 Received text input: '{text}'")

                    if text == "d" or text == "1":
                        self.telegram_manager.send_message(
                            "✅ Using default configuration"
                        )
                        return 1
                    elif text == "2":
                        self.telegram_manager.send_message(
                            "✅ Selected predefined strategies"
                        )
                        return 2
                    elif text == "3":
                        self.telegram_manager.send_message(
                            "✅ Selected custom strategy building"
                        )
                        return 3
                    else:
                        self.telegram_manager.send_message(
                            f"❌ Invalid selection '{text}'. Please click a button or type 1-3."
                        )

            time.sleep(1)

        # Timeout
        self.telegram_manager.send_message(
            f"⏰ No response received within {self.timeout_minutes} minutes. Using default configuration."
        )
        return 1

    def _wait_for_callback_response(self) -> Optional[str]:
        """Wait for user callback response."""
        start_time = time.time()
        while time.time() - start_time < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()
            for update in updates:
                if "callback_query" in update:
                    callback_data = update["callback_query"]["data"]
                    callback_id = update["callback_query"]["id"]

                    # Acknowledge the callback
                    self.telegram_manager.answer_callback_query(
                        callback_id, "Selection received"
                    )
                    return callback_data

            time.sleep(1)

        return "general_default"  # Default to no general setup on timeout

    def _ask_use_random_strategy(self) -> bool:
        """Ask if user wants to use a random strategy (first question)."""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "🎲 Yes - Surprise Me!", "callback_data": "random_yes"},
                    {"text": "🎯 No - I'll Choose", "callback_data": "random_no"},
                ],
                [{"text": "🔧 Default (No)", "callback_data": "random_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "🎲 **Random Strategy**\n\n"
            "Do you want to use a random investment strategy?\n\n"
            "This will select a surprise strategy from our collection of "
            "powerful investment scenarios featuring companies from around the world.\n\n"
            "**Default: Yes (random strategy)**\n\n"
            "👇 **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_callback_response()

        if response == "random_no":
            LOGGER.info("🎯 User chose to select strategy manually")
            self.telegram_manager.send_message(
                "🎯 You'll choose your strategy manually"
            )
            return False
        else:
            # Default to random (includes "random_yes", "random_default", and timeout)
            LOGGER.info("🎲 Using random strategy (user choice or default)")
            self.telegram_manager.send_message(
                "🎲 Selecting a random strategy for you..."
            )
            return True

    def _handle_random_strategy_with_adjustments(self) -> Optional[Dict[str, Any]]:
        """Handle random strategy selection with adjustment options."""
        while True:
            # Select a random strategy
            random_config = self._handle_random_strategy()
            if random_config is None:
                return None

            # Ask if user wants to change something
            change_choice = self._ask_change_strategy(random_config)

            if change_choice == "no":
                # User is satisfied with the random strategy - move to used folder
                self._move_to_used_folder(random_config.get("_selected_file"))
                return random_config
            elif change_choice == "skip":
                # User wants to skip this strategy and get a new random one
                self._move_random_to_skipped(random_config.get("_selected_file"))
                continue  # Loop to select a new random strategy
            elif change_choice == "yes":
                # User wants to adjust the strategy - move to used folder
                adjusted_config = self._handle_strategy_adjustments(random_config)
                if adjusted_config:
                    self._move_to_used_folder(random_config.get("_selected_file"))
                return adjusted_config

        return None

    def _ask_change_strategy(self, config: Dict[str, Any]) -> str:
        """Ask if user wants to change the selected random strategy."""
        # Extract strategy info for display
        display_text = config.get("random_description", "Random Strategy")
        general = config.get("general", {})
        strategies = config.get("strategies", {})

        # Create summary message
        summary = f"🎲 **Selected Strategy**\n\n"
        summary += f"**{display_text[:100]}...**\n\n"
        summary += f"📊 **Configuration:**\n"
        summary += f"• Years: {general.get('years_ago', 'N/A')}\n"
        summary += f"• Amount: ${general.get('investment_amount', 'N/A'):,}\n"
        summary += f"• Frequency: {general.get('investment_kind', 'N/A').title()}\n"
        summary += f"• Strategies: {len(strategies)}\n\n"
        summary += "Do you want to change something?"

        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ No - Use This", "callback_data": "change_no"},
                    {"text": "⏭️ Skip - New Random", "callback_data": "change_skip"},
                ],
                [{"text": "🔧 Yes - Adjust", "callback_data": "change_yes"}],
                [{"text": "🔧 Default (No)", "callback_data": "change_default"}],
            ]
        }

        self.telegram_manager.send_message(summary, reply_markup=keyboard)

        response = self._wait_for_callback_response()

        if response == "change_yes":
            LOGGER.info("🔧 User chose to adjust the strategy")
            return "yes"
        elif response == "change_skip":
            LOGGER.info("⏭️ User chose to skip and get new random strategy")
            return "skip"
        else:
            LOGGER.info("✅ User chose to use the strategy as-is")
            return "no"

    def _handle_random_strategy(self) -> Optional[Dict[str, Any]]:
        """Handle random strategy selection from the random options folder."""
        LOGGER.info("🎲 Starting random strategy selection")

        # Get the random options directory
        from src.utils.paths import get_assets_dir

        random_dir = get_assets_dir() / "options" / "random"

        if not random_dir.exists():
            LOGGER.error(f"❌ Random options directory not found: {random_dir}")
            self.telegram_manager.send_message(
                "❌ Random options directory not found. Please contact administrator."
            )
            return None

        # Get all available TOML files in the random directory
        toml_files = list(random_dir.glob("*.toml"))

        if not toml_files:
            LOGGER.error(f"❌ No random options found in: {random_dir}")
            self.telegram_manager.send_message(
                "❌ No random options available. Please contact administrator."
            )
            return None

        # Select a random file
        selected_file = random.choice(toml_files)
        LOGGER.info(f"🎲 Selected random option: {selected_file.name}")

        try:
            # Load the selected configuration
            import tomllib

            with open(selected_file, "rb") as f:
                config_data = tomllib.load(f)

            # Extract display information
            display_text = config_data.get("display_text", "Random Strategy")
            description = config_data.get("description", "Random investment strategy")

            # Send notification about the selected strategy
            self.telegram_manager.send_message(
                f"🎲 **Random Strategy Selected!**\n\n"
                f"**{display_text}**\n\n"
                f"{description}"
            )

            # Don't move the file yet - wait for user decision

            # Return the configuration (excluding display metadata but keeping description for TikTok)
            result_config = {
                k: v for k, v in config_data.items() if k not in ["display_text"]
            }

            # Store the description for TikTok use
            result_config["random_description"] = description

            # Store the selected file path for potential skipping
            result_config["_selected_file"] = selected_file

            LOGGER.info(f"✅ Successfully loaded random strategy: {display_text}")
            return result_config

        except Exception as e:
            LOGGER.error(f"❌ Failed to load random strategy from {selected_file}: {e}")
            self.telegram_manager.send_message(
                f"❌ Failed to load random strategy. Please try again."
            )
            return None

    def _move_to_used_folder(self, file_path: Path) -> None:
        """Move a file to the 'used' subfolder in the options directory."""
        if not file_path or not file_path.exists():
            LOGGER.warning(f"⚠️ File not found for moving to used folder: {file_path}")
            return
        try:
            move_file_to_used_folder(file_path)
        except Exception as e:
            LOGGER.error(f"❌ Failed to move file to used folder: {e}")
            # Don't fail the whole process if we can't move the file

    def _move_random_to_skipped(self, file_path: Path) -> None:
        """Move a skipped configuration file to the 'skipped' subfolder."""
        if not file_path or not file_path.exists():
            return

        try:
            # Create the "skipped" directory if it doesn't exist
            skipped_dir = (
                file_path.parent / "skipped" / datetime.now().strftime("%Y%m%d")
            )
            skipped_dir.mkdir(parents=True, exist_ok=True)

            # Move the file to the skipped directory
            destination = skipped_dir / file_path.name
            shutil.move(str(file_path), str(destination))

            LOGGER.info(f"📁 Moved skipped config to: {destination}")

        except Exception as e:
            LOGGER.error(f"❌ Failed to move file to skipped folder: {e}")
            # Don't fail the whole process if we can't move the file

    def _handle_strategy_adjustments(
        self, config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Handle strategy adjustments (Add, Adjust, Both)."""
        # Ask what type of adjustments the user wants
        adjustment_type = self._ask_adjustment_type()

        if adjustment_type == "add":
            # Add more strategies to the existing config
            return self._add_strategies_to_config(config)
        elif adjustment_type == "adjust":
            # Adjust existing configuration
            return self._adjust_existing_config(config)
        elif adjustment_type == "both":
            # First adjust existing, then add more
            adjusted_config = self._adjust_existing_config(config)
            if adjusted_config:
                return self._add_strategies_to_config(adjusted_config)
            return None
        else:
            # Default to no changes
            return config

    def _ask_adjustment_type(self) -> str:
        """Ask what type of adjustments the user wants to make."""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "➕ Add - More Strategies", "callback_data": "adjust_add"},
                    {
                        "text": "🔧 Adjust - Modify Config",
                        "callback_data": "adjust_adjust",
                    },
                ],
                [{"text": "🔄 Both - Add & Adjust", "callback_data": "adjust_both"}],
                [{"text": "🔧 Default (Both)", "callback_data": "adjust_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "🔧 **Adjustment Options**\n\n"
            "What would you like to do?\n\n"
            "• **Add**: Add more investment strategies\n"
            "• **Adjust**: Modify existing configuration\n"
            "• **Both**: Add strategies and adjust config\n\n"
            "**Default: Both**\n\n"
            "👇 **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_callback_response()

        if response == "adjust_add":
            LOGGER.info("➕ User chose to add more strategies")
            return "add"
        elif response == "adjust_adjust":
            LOGGER.info("🔧 User chose to adjust existing config")
            return "adjust"
        else:
            LOGGER.info("🔄 User chose both add and adjust (or default)")
            return "both"

    def _add_strategies_to_config(
        self, config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Add more strategies to the existing configuration."""
        self.telegram_manager.send_message(
            "➕ **Adding More Strategies**\n\n"
            "You can now add additional investment strategies to your configuration."
        )

        # Use the existing strategy builder to add more strategies
        additional_strategies = self.strategy_builder.build_strategies_interactive()

        if additional_strategies and "strategies" in additional_strategies:
            # Merge the new strategies with existing ones
            if "strategies" not in config:
                config["strategies"] = {}

            config["strategies"].update(additional_strategies["strategies"])

            self.telegram_manager.send_message(
                f"✅ Added {len(additional_strategies['strategies'])} new strategies!"
            )

        return config

    def _adjust_existing_config(
        self, config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Adjust the existing configuration."""
        self.telegram_manager.send_message(
            "🔧 **Adjusting Configuration**\n\n"
            "You can now modify the existing configuration parameters."
        )

        # Allow user to adjust general config
        adjusted_general = (
            self.general_config_builder.build_general_config_interactive()
        )

        if adjusted_general:
            config["general"] = adjusted_general
            self.telegram_manager.send_message("✅ Updated general configuration!")

        # Allow user to adjust individual strategies
        if "strategies" in config:
            config = self._adjust_strategy_timings(config)

        return config

    def _adjust_strategy_timings(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Allow user to adjust timing strategies for individual strategies."""
        strategies = config.get("strategies", {})

        for strategy_id, strategy_config in strategies.items():
            current_timing = strategy_config.get("timing", "regular")

            # Ask if user wants to change timing for this strategy
            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "📈 Regular", "callback_data": "timing_regular"},
                        {"text": "⬆️ Peaks", "callback_data": "timing_peaks"},
                    ],
                    [
                        {"text": "⬇️ Lows", "callback_data": "timing_lows"},
                        {"text": "⏭️ Skip", "callback_data": "timing_skip"},
                    ],
                ]
            }

            self.telegram_manager.send_message(
                f"🎯 **Adjust Timing: {strategy_config.get('name', strategy_id)}**\n\n"
                f"Current timing: **{current_timing.title()}**\n\n"
                f"• **Regular**: Standard dollar-cost averaging\n"
                f"• **Peaks**: Buy at monthly highs (worst timing)\n"
                f"• **Lows**: Buy at monthly lows (best timing)\n\n"
                f"Choose new timing or skip:",
                reply_markup=keyboard,
            )

            response = self._wait_for_callback_response()

            if response == "timing_regular":
                strategies[strategy_id]["timing"] = "regular"
                self.telegram_manager.send_message("✅ Set to Regular timing")
            elif response == "timing_peaks":
                strategies[strategy_id]["timing"] = "peaks"
                self.telegram_manager.send_message("✅ Set to Peaks timing")
            elif response == "timing_lows":
                strategies[strategy_id]["timing"] = "lows"
                self.telegram_manager.send_message("✅ Set to Lows timing")
            else:
                self.telegram_manager.send_message("⏭️ Skipped timing adjustment")

        return config

    def _handle_predefined_strategies(self) -> Optional[Dict[str, Any]]:
        """Handle predefined strategies submenu."""
        predefined_options = self.option_loader.load_predefined_options()

        if not predefined_options:
            self.telegram_manager.send_message(
                "❌ No predefined strategies available. Using default configuration."
            )
            return None

        # Create inline keyboard with strategy options
        keyboard_buttons = []

        # Add strategy options (max 2 per row for better layout)
        for i, option in enumerate(predefined_options):
            button = {"text": option.display_text, "callback_data": f"strategy_{i}"}

            # Add 2 buttons per row, or 1 if it's the last odd button
            if i % 2 == 0:
                keyboard_buttons.append([button])
            else:
                keyboard_buttons[-1].append(button)

        # Add default option button
        keyboard_buttons.append(
            [{"text": "🔧 Use Default Config", "callback_data": "strategy_default"}]
        )

        keyboard = {"inline_keyboard": keyboard_buttons}

        # Show predefined options with description
        message_lines = [
            # "📊 **Predefined Strategy Options**\n",
            # f"⏱️ You have **{self.timeout_minutes} minutes** to choose.\n",
            "📋 **Available Strategies:**\n"
        ]

        for option in predefined_options:
            message_lines.append(f"• **{option.display_text}**")
            message_lines.append(f"{option.description}\n")

        message_lines.append("👇 **Click on a strategy card below to select it**")

        self.telegram_manager.send_message(
            "\n".join(message_lines), reply_markup=keyboard
        )

        # Wait for selection
        selected_index = self._wait_for_predefined_response(len(predefined_options))

        if selected_index == 0:  # Default or timeout
            return None

        selected_option = predefined_options[selected_index - 1]
        self.telegram_manager.send_message(
            f"✅ **Selected: {selected_option.display_text}**\n\n"
            f"{selected_option.description}\n\n"
        )

        return selected_option.strategy_data

    def _wait_for_predefined_response(self, max_options: int) -> int:
        """Wait for predefined menu response (button clicks or text messages)."""
        start_time = time.time()
        menu_start_timestamp = int(start_time)

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                # Handle callback queries (button presses)
                if "callback_query" in update:
                    callback_query = update["callback_query"]
                    callback_data = callback_query.get("data", "")
                    callback_id = callback_query.get("id", "")

                    LOGGER.info(f"🎯 Received strategy button press: '{callback_data}'")

                    if callback_data == "strategy_default":
                        self.telegram_manager.answer_callback_query(
                            callback_id, "Using default configuration"
                        )
                        self.telegram_manager.send_message(
                            "Using default configuration"
                        )
                        return 0
                    elif callback_data.startswith("strategy_"):
                        try:
                            strategy_index = int(callback_data.split("_")[1])
                            if 0 <= strategy_index < max_options:
                                self.telegram_manager.answer_callback_query(
                                    callback_id, "Strategy selected"
                                )
                                return strategy_index + 1  # Convert to 1-based index
                            else:
                                self.telegram_manager.answer_callback_query(
                                    callback_id, "Invalid strategy"
                                )
                        except (ValueError, IndexError):
                            self.telegram_manager.answer_callback_query(
                                callback_id, "Invalid selection"
                            )

                # Handle text messages (fallback)
                elif "message" in update and "text" in update["message"]:
                    # Only process messages sent after the menu was shown
                    message_timestamp = update["message"].get("date", 0)
                    if message_timestamp < menu_start_timestamp:
                        continue

                    text = update["message"]["text"].strip().lower()

                    if text == "d":
                        self.telegram_manager.send_message(
                            "Using default configuration"
                        )
                        return 0

                    try:
                        choice = int(text)
                        if 1 <= choice <= max_options:
                            return choice
                        else:
                            self.telegram_manager.send_message(
                                f"❌ Invalid choice. Please click a button or type 1-{max_options}."
                            )
                    except ValueError:
                        self.telegram_manager.send_message(
                            f"❌ Invalid input '{text}'. Please click a button or type 1-{max_options}."
                        )

            time.sleep(1)

        # Timeout
        self.telegram_manager.send_message(
            f"⏰ No response received within {self.timeout_minutes} minutes. Using default configuration."
        )
        return 0

    def _handle_custom_strategies(
        self, base_config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Handle custom strategy building directly in chat."""
        LOGGER.info("🏗️ Starting custom strategy building")

        # Build strategies using the strategy builder
        strategies = self.strategy_builder.build_strategies_interactive()

        if not strategies:
            LOGGER.warning("⚠️ No strategies created, using default configuration")
            self.telegram_manager.send_message(
                "⚠️ No strategies were created. Using default configuration."
            )
            return None

        # Create result config with custom strategies but keep general settings from base
        result_config = dict(base_config)
        result_config["strategies"] = strategies

        self.telegram_manager.send_message(
            f"✅ **Custom Configuration Created!**\n\n"
            f"Created {len(strategies)} strategies:\n"
            + "\n".join(
                [
                    f"• {strategy['name']} ({strategy['ticker']})"
                    for strategy in strategies.values()
                ]
            )
        )

        return result_config
