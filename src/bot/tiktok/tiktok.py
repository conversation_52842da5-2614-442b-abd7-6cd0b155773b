import os
from pathlib import Path
import random
from typing import Optional
from tiktok_uploader.upload import upload_video
from src.logger import get_logger
from src.music import MusicManager
from src.config import ReelStonksManager

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"
TIKTOK_DESCRIPTION_TEMPLATE = {
    "introduction": [
        "Stonks only go up, right? What do you think?",
        "Can you beat the market?",
        "Investment is a journey, not a destination. What's your story?",
        "The market is unpredictable. Can you beat the odds?",
        "Investment is a rollercoaster. Can you handle the ride?",
        "What would you pick now?",
        "Which one would you have chosen? Which one would you pick now?",
    ],
    "disclaimer": [
        "DISCLAIMER: This is a fictional scenario created for entertainment purposes only. Past performance is not indicative of future results. Always do your own research before making any investment decisions. Investment involves risk. Only invest with money you can afford to lose.",
    ],
    "community_question": [
        "What do you wanna see next? Tell me in the comments!",
        "Which epic development would you like to see next? Tell me in the comments!",
        "What's your take on this? Tell me in the comments!",
    ],
    "hashtag": [
        "#reelstonks #stockmarket #rich #retire #wealthy #wealth #oldmoney #stocks #finance #investing #viral #money #crypto #fyp #foryoupage",
    ],
}


def generate_tiktok_description_from_strategies(
    strategies: list, investment_amount: int, years_ago: int, interval: str
) -> str:
    """Generate a dynamic TikTok description based on a list of strategies."""

    interval_str = (
        f"on a {interval.lower()}ly basis for {years_ago} years"
        if interval != "once"
        else f"once {years_ago} years ago"
    )
    # Define section based on general configuration
    general_section = (
        f"Look at this! What if you had invested ${investment_amount} {interval_str}?"
    )

    # Define section based on strategies
    strategy_sections = ["Here are the simulated scenarios:"]
    for strategy in strategies:
        strategy_sections.append(
            f"- {strategy.display_name} ({'with' if strategy.reinvest_dividends else 'without'} reinvested dividends{f', buying at {strategy.investment_timing}' if strategy.investment_timing != 'regular' else ''})"
        )

    return f"{general_section}\n\n{'\n'.join(strategy_sections)}"


def generate_tiktok_description(
    dynamic_description: str,
    music_creator: str | bool = False,
    music_link: str | bool = False,
) -> str:
    """Generate a dynamic TikTok description with a disclaimer and hashtags.

    Args:
        music_creator: The name of the music creator.
        music_link: The link for the music.

    Returns:
        Dynamic TikTok description.
    """
    introduction = random.choice(TIKTOK_DESCRIPTION_TEMPLATE["introduction"])
    disclaimer = random.choice(TIKTOK_DESCRIPTION_TEMPLATE["disclaimer"])
    community_question = random.choice(
        TIKTOK_DESCRIPTION_TEMPLATE["community_question"]
    )
    hashtag = random.choice(TIKTOK_DESCRIPTION_TEMPLATE["hashtag"])

    music_section = ""
    if music_creator:
        music_section += f"Music by {music_creator}\n\n"
    if music_link:
        music_section += f"Check out: {music_link}\n\n"

    return f"{introduction}\n\n{dynamic_description}\n{community_question}\n\n{music_section}{disclaimer}\n\n{hashtag}"


def filter_bmp_characters(text: str) -> str:
    """Filter out characters outside the Basic Multilingual Plane (BMP).

    ChromeDriver only supports characters in the BMP (Unicode code points 0-65535).
    This function removes any characters outside this range to prevent upload errors.

    Args:
        text (str): Input text that may contain non-BMP characters

    Returns:
        str: Text with non-BMP characters removed
    """
    return "".join(char for char in text if ord(char) <= 0xFFFF)


def generate_description(config: ReelStonksManager) -> str:
    """Generate a description for TikTok upload."""
    # Otherwise use the dynamic description generation
    dynamic_description = generate_tiktok_description_from_strategies(
        config.strategies,
        config.investment_amount,
        config.years_ago,
        config.interval_title,
    )

    tiktok_description = generate_tiktok_description(dynamic_description)

    # Check if this is a random strategy with a custom description
    if hasattr(config, "random_description") and config.random_description:
        return f"{tiktok_description}\n\n-----------------------\nRandom Description:\n\n{config.random_description}"

    return tiktok_description


def titktok_upload_manager(
    config: ReelStonksManager,
    video_path: str,
    music_filename: Optional[str] = None,
    cookies_file: str = COOKIE_FILE,
    headless: bool = True,
    privacy_type: str = "private",
) -> bool:
    """Uploads a video to TikTok using a cookies file for authentication.

    Creates a dynamic description based on the music used in the video by utilizing
    the existing MusicManager and creators.toml configuration.

    Args:
        video_path (str): The path to the video file.
        music_filename (Optional[str]): The music filename to get attribution for.
            If None, uses the first available music from MusicManager.
        cookies_file (str, optional): The path to the cookies file. Defaults to COOKIE_FILE.
        headless (bool, optional): Whether to run in headless mode. Defaults to True.
        privacy_type (str, optional): Privacy setting for the video. Defaults to "private".

    Returns:
        bool: True if the video was successfully uploaded, False otherwise.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(
            f"❌ Cookie file not found! Please ensure '{cookies_file}' exists."
        )
        return False

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return False

    try:
        # Get dynamic description from strategies
        dynamic_description = generate_tiktok_description_from_strategies(
            config.strategies,
            config.investment_amount,
            config.years_ago,
            config.interval_title,
        )
        # Load music TOML data and generate description based on creator and link
        music_toml_data = None
        try:
            # Skip music processing if explicitly disabled
            if music_filename != "DISABLED":
                music_manager = MusicManager()
                if music_manager.has_music():
                    selected_music = music_manager.select_music(music_filename)
                    if selected_music:
                        # Load the full TOML config to get creator and link
                        import toml

                        config_file = music_manager.music_dir / "creators.toml"
                        if config_file.exists():
                            config_data = toml.load(config_file)
                            music_toml_data = config_data.get(
                                selected_music.filename, {}
                            )
            else:
                LOGGER.info(
                    "🔇 Music disabled, skipping music data for TikTok description"
                )
        except Exception as e:
            LOGGER.warning(f"Could not load music data: {e}")

        # Generate description with different hashtag approaches
        if music_toml_data:
            # Try different hashtag styles - change this to test different approaches
            description = generate_tiktok_description(
                dynamic_description,
                music_toml_data.get("creator", False),
                # music_toml_data.get("link", False), # Ignore link for now
            )
        else:
            # Fallback to static description
            LOGGER.info("Using fallback static description")
            description = generate_tiktok_description(dynamic_description)

        # Filter description to remove non-BMP characters
        filtered_description = filter_bmp_characters(description)
        LOGGER.info(f"Using description: {filtered_description}")

        # Upload the video
        upload_video(
            filename=video_path,
            cookies=cookies_file,
            description=filtered_description,
            headless=headless,
            privacy_type=privacy_type,
        )
        LOGGER.info(
            f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!"
        )
        return True
    except Exception as e:
        LOGGER.error(f"❌ An error occurred: {e}")
        return False
