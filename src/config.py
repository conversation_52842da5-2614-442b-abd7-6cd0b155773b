from datetime import datetime
from dateutil.relativedelta import relativedelta
from typing import Dict, Any, Optional
from src.invest import (
    InvestOnce,
    InvestDaily,
    InvestWeekly,
    InvestMonthly,
    InvestQuarterly,
    InvestYearly,
    InvestMonthlyPeaks,
    InvestMonthlyLows,
)
from src.invest.invest import InvestFactory
from src.invest.strategy import InvestmentStrategy
from src.logger import get_logger

LOGGER = get_logger(__name__)
from src.utils.dict import get_value_by_key_condition
from src.logger import get_logger
from src.bot.telegram.constants import TelegramConfigLoader

LOGGER = get_logger(__name__)


class ReelStonksManager:
    """
    Configuration class to hold all runtime parameters.
    Accepts optional config_dict for values from a TOML file.
    """

    _INVESTMENT_SCHEMA = {
        "once": InvestOnce,
        ("daily", "regular"): InvestDaily,
        ("weekly", "regular"): InvestWeekly,
        ("monthly", "regular"): InvestMonthly,
        ("quarterly", "regular"): InvestQuarterly,
        ("yearly", "regular"): InvestYearly,
        ("monthly", "peaks"): InvestMonthlyPeaks,
        ("monthly", "lows"): InvestMonthlyLows,
    }
    _INTERVAL_MAP = {
        "once": "Once",
        "daily": "Day",
        "weekly": "Week",
        "monthly": "Month",
        "quarterly": "Quarter",
        "yearly": "Year",
    }

    @staticmethod
    def _load_telegram_config(config_dict: dict) -> dict:
        """Load Telegram configuration from environment variables and [telegram] section in config.toml."""
        telegram_cfg = config_dict.get("telegram", {})
        return TelegramConfigLoader._load_from_env(telegram_cfg)

    def _init_general_config(self, config_dict: dict) -> None:
        """Set-Up general configuration from [general] section in config.toml."""
        # Get general config
        general_cfg = config_dict.get("general", {})
        self.years_ago: int = general_cfg.get("years_ago", 20)
        self.investment_amount: int = general_cfg.get("investment_amount", 1_000)
        self.investment_kind: str = general_cfg.get("investment_kind", "monthly")

        # Calculate dynamic animation duration based on years_ago
        # Linear interpolation: 28s for years_ago >= 25, 12s for years_ago <= 8
        if self.years_ago >= 25:
            calculated_duration = 28
        elif self.years_ago <= 8:
            calculated_duration = 12
        else:
            # Linear interpolation between 8-25 years: 12-28 seconds
            calculated_duration = 12 + (28 - 12) * (self.years_ago - 8) / (25 - 8)
            calculated_duration = int(round(calculated_duration))

        # Allow override from config, but use calculated duration as default
        self.animation_duration_seconds: int = general_cfg.get(
            "animation_duration_seconds", calculated_duration
        )
        self.fps: int = general_cfg.get("fps", 28)
        self.tax_rate = general_cfg.get("tax_rate", 0.26375)
        self.tax_free_return_threshold_per_annu = general_cfg.get(
            "tax_free_return_threshold_per_annu", 1_000
        )
        self.save_data: bool = general_cfg.get("save_data", False)
        self.data_frequency_timestamp = general_cfg.get(
            "data_frequency_timestamp", "%Y-%m"
        )

    def _init_task_config(self, config_dict: dict) -> None:
        """Set-Up task configuration from [task] section in config.toml."""
        # TODO: Wrap task types if tasks is True
        # Get task config
        task_cfg = config_dict.get("tasks", {})
        self.task_random: bool = task_cfg.get("random", False)
        self.task_count = task_cfg.get("count", "all")  # Can be "all" or integer
        self.task_zip: bool = task_cfg.get("zip", False)

    @staticmethod
    def _load_investment_objects(
        strategies: list[InvestmentStrategy],
        investment_kind: str,
    ) -> tuple[list[str], dict[str, InvestFactory]]:
        """Get the investment object for a given investment kind and timing."""

        # Define investment_objects as empty dict
        investment_objects = {}
        failed_strategies = []
        # Iterate over all unqiue investment stratgeies
        for strategy in strategies:
            # Special handling for "once" investment kind - it doesn't use timing strategies
            if investment_kind == "once":
                _investment_object = ReelStonksManager._INVESTMENT_SCHEMA.get("once")
            else:
                # Get corresponding investment object for a combination of investment_kind and timing
                _investment_object = get_value_by_key_condition(
                    d=ReelStonksManager._INVESTMENT_SCHEMA,
                    contains=(investment_kind, strategy.investment_timing),
                )

            # Log error if investment object is not found
            if not _investment_object:
                if investment_kind == "once":
                    LOGGER.error(
                        f"❌ Unsupported 'investment_object' for 'once' investment kind. {strategy.strategy_id} skipped."
                    )
                else:
                    LOGGER.error(
                        f"❌ Unsupported 'investment_object' for arguments: {investment_kind, strategy.investment_timing}. {strategy.strategy_id} skipped."
                    )
                failed_strategies.append(strategy.strategy_id)
                continue
            investment_objects[strategy.strategy_id] = _investment_object

        return investment_objects, failed_strategies

    def _init_dates(self) -> None:
        """Initialize start and end dates based on years_ago."""
        # Derived fields
        self.interval: str = "1d"
        self.interval_title: str = self._INTERVAL_MAP[self.investment_kind]
        self.today: datetime = datetime.now()
        self.start_date: datetime = self.today - relativedelta(years=self.years_ago)
        self.today_str: str = self.today.strftime("%Y-%m-%d")
        self.start_date_str: str = self.start_date.strftime("%Y-%m-%d")

    @staticmethod
    def _load_song_to_use(config_dict: dict) -> str:
        """Load music configuration from [music] section in config.toml."""
        music_cfg = config_dict.get("music", {})

        # Check for disabled music with underscore prefix (_use)
        # This handles cases like: _use = "filename.mp3" # Turn off
        if "_use" in music_cfg:
            LOGGER.info("🔇 Music explicitly disabled with underscore prefix (_use)")
            return "DISABLED"

        # Return normal use value or None if not present
        return music_cfg.get("use", None)

    @staticmethod
    def _load_upload_config(config_dict: dict) -> dict:
        """Load update configuration from [update] section in config.toml."""
        upload_cfg = config_dict.get("upload", {})
        return upload_cfg

    def __init__(self, config_dict: dict = None):
        """Initialize the Config object with runtime parameters.

        Loads configuration values from a dictionary (typically from a TOML file)
        and sets up all necessary parameters for investment analysis and animation.
        Missing values are filled with sensible defaults.

        Args:
            config_dict (dict, optional): Dictionary containing configuration values.
                Expected structure:
                - ticker (dict): Mapping of ticker symbols to company names
                - general (dict): General configuration with keys:
                    - years_ago (int): Number of years back to analyze
                    - investment_amount (int): Amount to invest per interval
                    - investment_kind (str): Investment frequency ("daily", "weekly", "monthly", "quarterly", "yearly")
                    - animation_duration_seconds (int): Duration of animation in seconds
                    - fps (int): Frames per second for animation
                    - tax_rate (float): Tax rate applied to dividends
                    - tax_free_return_threshold_per_annu (float): Annual tax-free dividend threshold
                    - save_data (bool): Whether to save fetched stock data to CSV files
                - music (dict): Music configuration with keys:
                    - use (str): Filename of preferred music track from assets/music directory
                - dividends (dict): Mapping of ticker symbols to boolean dividend reinvestment flags
                Defaults to None, which uses built-in defaults.

        Raises:
            ValueError: If investment_kind is not one of the supported values
                ("daily", "weekly", "monthly", "quarterly", "yearly").
        """
        LOGGER.activate()
        config_dict = config_dict or {}

        # Parse strategies (new format) or fall back to ticker format (old format)
        self.strategies = self._parse_strategies(config_dict)
        self.failed_strategies = []

        # Set-Up general config arguments and dates
        self._init_general_config(config_dict)
        self._init_task_config(config_dict)
        self._init_dates()

        # Parse telegram config
        self.telegram_cfg = self._load_telegram_config(config_dict)

        # Disable Telegram if running random tasks from master config
        if self.task_random:
            self.telegram_cfg.enabled = False

        # Parse music configuration
        self.use_music = self._load_song_to_use(config_dict)

        # Load investment objects
        self.investment_objects, self.failed_strategies = self._load_investment_objects(
            self.strategies, self.investment_kind
        )

        # Get upload config from config.toml
        self.upload_cfg = self._load_upload_config(config_dict)

        # Load random description if available (for TikTok)
        self.random_description = config_dict.get("random_description", None)

        LOGGER.info(f"✅ Config loaded with {len(self.strategies)} strategies")

    def _parse_strategies(
        self, config_dict: Dict[str, Any]
    ) -> list[InvestmentStrategy]:
        """Parse investment strategies from configuration.

        Supports both new strategy format and legacy ticker format.

        Args:
            config_dict: Configuration dictionary from TOML file.

        Returns:
            List of InvestmentStrategy objects.
        """
        strategies = []

        # Check for new strategy format
        if "strategies" in config_dict:
            # Get strategy configs
            strategy_configs = config_dict["strategies"]
            # Iterate over strategy configs
            for strategy_id, strategy_config in strategy_configs.items():
                # Initialize strategy object
                strategy = InvestmentStrategy(
                    strategy_id=strategy_id,
                    ticker_symbol=strategy_config["ticker"],
                    display_name=strategy_config.get("name", strategy_config["ticker"]),
                    reinvest_dividends=strategy_config.get("reinvest_dividends", False),
                    investment_timing=strategy_config.get("timing", "regular"),
                    **{
                        k: v
                        for k, v in strategy_config.items()
                        if k not in ["ticker", "name", "reinvest_dividends", "timing"]
                    },
                )
                strategies.append(strategy)
        else:
            # Fall back to legacy ticker format
            ticker_config = config_dict.get("ticker", {"AAPL": "Apple"})
            dividend_config = config_dict.get("dividends", {})

            for ticker_symbol, display_name in ticker_config.items():
                strategy = InvestmentStrategy(
                    strategy_id=ticker_symbol,
                    ticker_symbol=ticker_symbol,
                    display_name=display_name,
                    reinvest_dividends=dividend_config.get(ticker_symbol, False),
                    investment_timing="regular",
                )
                strategies.append(strategy)

        return strategies

    def get_strategy(self, strategy_id: str) -> Optional[InvestmentStrategy]:
        """Get a strategy by its ID.

        Args:
            strategy_id: The strategy identifier.

        Returns:
            InvestmentStrategy object or None if not found.
        """
        for strategy in self.strategies:
            if strategy.strategy_id == strategy_id:
                return strategy
        return None
