import numpy as np
import pandas as pd

from src.logger import get_logger
from src.utils.numba import _invest
from src.utils.enum import TimestampFrequencyEnum


LOGGER = get_logger(__name__)


class InvestFactory:
    """An abstract base class for investment strategies. The project method is implemented here as
    its logic is generic, relying on the _get_investment_mask from child classes.
    """

    def __init__(
        self,
        df: pd.DataFrame,
        investment_amount: float,
        tax_rate: float = 0.0,
        tax_free_return_threshold_per_annu: float = 0.0,
    ):
        """Initialize the AbstractInvest instance.

        Sets up the investment strategy with price and dividend data, investment
        parameters, and tax settings. Uses the optimized njit _invest function
        for fast calculations.

        Args:
            price_time_series (pd.Series): Time series of stock prices indexed by date.
            dividend_time_series (pd.Series): Time series of dividend payments per share
                indexed by date.
            investment_amount (float): The amount to invest at each investment interval.
            tax_rate (float, optional): Tax rate applied to dividends above the
                tax-free threshold. Defaults to 0.0.
            tax_free_return_threshold_per_annu (float, optional): Annual dividend
                amount that is tax-free. Dividends above this threshold are taxed.
                Defaults to 0.0.
        """
        LOGGER.activate()
        self.df = df
        self.investment_amount = investment_amount
        self.tax_rate = tax_rate
        self.tax_free_return_threshold_per_annu = tax_free_return_threshold_per_annu

        LOGGER.info(" --- InvestmentFactory initialized")

    def _get_target_dates(self, frequency: pd.offsets.BaseOffset) -> pd.Series:
        """Get target dates for investment intervals.

        Args:
            frequency (pd.offsets.BaseOffset): The frequency of the investment
                intervals (e.g., pd.DateOffset(months=1) for monthly).

        Returns:
            pd.Series: Series of target dates for investment intervals.
        """
        return pd.date_range(
            start=self.df.index[0],
            end=self.df.index[-1],
            freq=frequency,
        )

    def _get_investment_mask(self, frequency: pd.offsets.BaseOffset):
        """Get the boolean mask that determines the investment days.

        This abstract method must be implemented by subclasses to define
        which days investments should be made based on the investment strategy
        (daily, weekly, monthly, quarterly, or yearly).

        Returns:
            pd.Series: Boolean series indexed by dates, where True indicates
                an investment should be made on that date.
        """
        target_dates = self._get_target_dates(frequency)
        # Find the first available trading day on or after each target anniversary
        investment_indices = self.df.index.searchsorted(target_dates)
        # Ensure indices are within bounds
        investment_indices = investment_indices[investment_indices < len(self.df.index)]
        investment_dates = self.df.index[investment_indices].unique()
        return self.df.index.isin(investment_dates)

    def _get_yearly_periods(self) -> pd.Series:
        """Get boolean mask for the first occurrence of each year.

        Returns:
            pd.Series: Boolean series where True indicates the first trading day
                of each year in the time series.
        """
        return ~self.df.index.strftime("%Y").duplicated()

    def invest(
        self, reinvest_dividends: bool = False, timestamp_frequency: str = "%Y-%m"
    ) -> pd.DataFrame:
        """Project wealth accumulation using the optimized njit _invest function.

        Calculates investment performance using the fast, compiled _invest function
        that handles both dividend reinvestment and non-reinvestment scenarios with
        proper tax calculations.

        Args:
            reinvest_dividends (bool, optional): Whether to reinvest dividends.
                If True, dividends are used to purchase additional shares.
                If False, dividends are received as cash. Defaults to False.

        Returns:
            pd.DataFrame: DataFrame with columns:
                - Wealth: Total portfolio value
                - Invested Cash: Cash invested on each date
                - Number of Stocks: Total shares owned
                - Paid Gross Dividends: Gross dividends received
                - Paid Net Dividends: Net dividends after taxes
        """
        LOGGER.info(
            f" --- Calculating projection for {self.__class__.__name__} "
            f"({'Distributing' if reinvest_dividends else 'Accumulating'})."
        )

        # Validate timestamp_frequency and fall back to default if invalid
        try:
            timestamp_frequency = TimestampFrequencyEnum(timestamp_frequency).value
        except ValueError:
            LOGGER.warning(
                f"Invalid timestamp_frequency '{timestamp_frequency}'. "
                f"Falling back to default '{TimestampFrequencyEnum.YEARLY_MONTHLY.value}'."
            )
            timestamp_frequency = TimestampFrequencyEnum.YEARLY_MONTHLY

        # Check if there are any dividends to reinvest and warn if not
        if reinvest_dividends and (self.df["Dividends"].sum() == 0):
            LOGGER.warning(" --- No dividends to reinvest")

        LOGGER.info(f" --- Number of data points: {len(self.df)}")

        # Get investment and yearly period masks
        investment_mask = self._get_investment_mask()
        yearly_periods = self._get_yearly_periods()

        LOGGER.info(f" --- Number of investment days: {investment_mask.sum():,}")
        LOGGER.info(f" --- Number of yearly periods: {yearly_periods.sum():,}")

        # Call the optimized njit function
        (
            wealth,
            invested_cash,
            number_of_stocks,
            paid_gross_dividends,
            paid_net_dividends,
        ) = _invest(
            price=self.df["Close"].values,
            dividends=self.df["Dividends"].values,
            investment_amount=self.investment_amount,
            investment_periods=investment_mask,
            yearly_periods=yearly_periods,
            tax_rate=self.tax_rate,
            tax_free_return_threshold_per_annu=self.tax_free_return_threshold_per_annu,
            reinvest_dividends=reinvest_dividends,
        )

        # Create result DataFrame with all data
        result_df = pd.DataFrame(
            {
                "Wealth": wealth,
                "Invested Cash": invested_cash,
                "Number of Stocks": number_of_stocks,
                "Paid Gross Dividends": paid_gross_dividends,
                "Paid Net Dividends": paid_net_dividends,
            },
            index=self.df.index,
        )
        # Group by month and apply aggregation rules
        result_df = result_df.groupby(
            result_df.index.strftime(timestamp_frequency)
        ).agg(
            {
                "Wealth": "last",
                "Number of Stocks": "last",
                "Invested Cash": "sum",
                "Paid Gross Dividends": "sum",
                "Paid Net Dividends": "sum",
            }
        )

        result_df.index = pd.to_datetime(result_df.index)
        return result_df


class InvestOnce(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for a single investment at the start."""
        investment_mask = np.full(len(self.df), False, dtype=bool)
        investment_mask[0] = True
        return investment_mask


class InvestDaily(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for daily investments."""
        return np.full(len(self.df), True, dtype=bool)


class InvestWeekly(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for weekly investments."""
        return super()._get_investment_mask(pd.DateOffset(weeks=1))


class InvestMonthly(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for monthly investments."""
        return super()._get_investment_mask(pd.DateOffset(months=1))


class InvestQuarterly(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for quarterly investments."""
        return super()._get_investment_mask(pd.DateOffset(months=3))


class InvestYearly(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for yearly investments."""
        return super()._get_investment_mask(pd.DateOffset(years=1))


class InvestMonthlyPeaks(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for monthly investments when price is at all-time high."""
        peaks = self.df.groupby(self.df.index.strftime("%Y-%m"))["Close"].idxmax()
        return self.df.index.isin(peaks)


class InvestMonthlyLows(InvestFactory):

    def _get_investment_mask(self) -> np.ndarray:
        """Get investment mask for monthly investments when price is at all-time low."""
        lows = self.df.groupby(self.df.index.strftime("%Y-%m"))["Close"].idxmin()
        return self.df.index.isin(lows)
