import numpy as np
from src.utils.numba import _invest


def test_no_investment_or_dividends():
    """Test that wealth remains zero if no investments are made and no dividends are paid."""
    n = 5
    price = np.full(n, 100.0)
    dividends = np.zeros(n)
    investment_periods = np.full(n, False)
    yearly_periods = np.array([True, False, False, False, False])

    wealth, invested, stocks, _, _ = _invest(
        price, dividends, 1000, investment_periods, yearly_periods, 0.25, 1000, False
    )

    assert np.all(wealth == 0)
    assert np.all(invested == 0)
    assert np.all(stocks == 0)


def test_single_investment_no_dividends():
    """Test a single investment event without any dividends."""
    n = 5
    price = np.array([100.0, 110.0, 105.0, 120.0, 115.0])
    investment_periods = np.array([True, False, False, False, False])

    wealth, invested, stocks, _, _ = _invest(
        price, np.zeros(n), 1000, investment_periods, np.full(n, False), 0, 0, False
    )

    # Day 0: Invest 1000 at price 100 -> 10 stocks
    assert invested[0] == 1000
    assert stocks[0] == 10.0
    assert wealth[0] == 1000.0

    # Day 1: No investment, price is 110 -> wealth is 10 * 110 = 1100
    assert invested[1] == 0
    assert stocks[1] == 10.0
    assert wealth[1] == 1100.0

    # Final day: price is 115 -> wealth is 10 * 115 = 1150
    assert stocks[-1] == 10.0
    assert wealth[-1] == 1150.0


def test_dividends_no_reinvestment():
    """Test receiving dividends without reinvesting them."""
    n = 5
    price = np.full(n, 100.0)
    dividends = np.array([0, 0, 1.0, 0, 0])  # $1 dividend per share on day 2

    # Invest 1000 on day 0 to acquire 10 stocks.
    investment_amount = 1000.0
    investment_periods = np.array([True, False, False, False, False])
    yearly_periods = np.array([True, False, False, False, False])

    # Run the simulation only ONCE
    wealth, _, stocks, gross_div, net_div = _invest(
        price=price,
        dividends=dividends,
        investment_amount=investment_amount,
        investment_periods=investment_periods,
        yearly_periods=yearly_periods,
        tax_rate=0.0,
        tax_free_return_threshold_per_annu=1000.0,
        reinvest_dividends=False,
    )

    # Day 0: 10 stocks were acquired.
    assert stocks[0] == 10.0

    # Day 1: The number of stocks is carried over.
    assert stocks[1] == 10.0

    # Day 2: The 10 stocks held from the previous day generate a dividend.
    # 10 stocks * $1/share dividend = $10.0
    assert gross_div[2] == 10.0
    assert net_div[2] == 10.0  # No tax in this scenario

    # Wealth = (10 stocks * $100 price) + $10 cash from dividend
    assert wealth[2] == 1010.0


def test_dividends_with_reinvestment():
    """Test receiving and reinvesting dividends."""
    n = 5
    price = np.array([100.0, 100.0, 100.0, 100.0, 100.0])
    dividends = np.array([0, 0, 1.0, 0, 0])  # $1 dividend on day 2
    investment_periods = np.array(
        [True, False, True, False, False]
    )  # Invest day 0 and day 2

    wealth, _, stocks, _, net_div = _invest(
        price, dividends, 1000, investment_periods, np.full(n, False), 0, 1000, True
    )

    # Day 0: Invest 1000 -> 10 stocks
    assert stocks[0] == 10.0
    # Day 1: Stocks unchanged
    assert stocks[1] == 10.0
    # Day 2: Receive 10 * $1 = $10 dividend. Reinvest it.
    # Total investment on day 2 = 1000 (regular) + 10 (dividend) = 1010
    # New stocks bought = 1010 / 100 = 10.1
    # Total stocks = 10 (previous) + 10.1 (new) = 20.1
    assert net_div[2] == 10.0
    np.testing.assert_allclose(stocks[2], 20.1)
    # Wealth = 20.1 stocks * $100 price. Dividends are in stocks, not cash.
    np.testing.assert_allclose(wealth[2], 2010.0)


def test_tax_logic_crossing_threshold():
    """Test the tax calculation when a dividend payment crosses the tax-free threshold."""
    n = 5
    price = np.full(n, 100.0)
    dividends = np.array([0, 95.0, 10.0, 0, 0])  # Dividend per share

    # Setup: 10 initial stocks
    investment_periods = np.array([True, False, False, False, False])
    yearly_periods = np.array([True, False, False, False, False])

    _, _, _, gross_div, net_div = _invest(
        price, dividends, 10000, investment_periods, yearly_periods, 0.25, 1000, False
    )

    # Day 0: Invest 10000 at 100 -> 100 stocks
    # Day 1: Receive 100 * 95 = 9500 dividend. Threshold is 1000.
    # Taxable amount = 9500 - 1000 = 8500. Tax = 8500 * 0.25 = 2125
    # Net dividend = 9500 - 2125 = 7375
    assert gross_div[1] == 9500
    np.testing.assert_allclose(net_div[1], 7375.0)

    # Day 2: Receive 100 * 10 = 1000 dividend. Already over threshold.
    # Taxable amount = 1000. Tax = 1000 * 0.25 = 250
    # Net dividend = 1000 - 250 = 750
    assert gross_div[2] == 1000
    np.testing.assert_allclose(net_div[2], 750.0)


def test_yearly_tax_reset():
    """Test that the annual dividend counter resets each year."""
    n = 5
    price = np.full(n, 100.0)
    dividends = np.array([0, 120.0, 0, 80.0, 0])
    yearly_periods = np.array([True, False, False, True, False])  # New year on day 3

    # Setup: 10 initial stocks
    investment_periods = np.array([True, False, False, False, False])

    _, _, _, gross_div, net_div = _invest(
        price, dividends, 1000, investment_periods, yearly_periods, 0.25, 1000, False
    )

    # Day 0: Invest 1000 at 100 -> 10 stocks
    # Day 1: Receive 10 * 120 = 1200 dividend. Threshold is 1000.
    # Taxable amount = 1200 - 1000 = 200. Tax = 200 * 0.25 = 50.
    # Net dividend = 1200 - 50 = 1150
    assert gross_div[1] == 1200
    np.testing.assert_allclose(net_div[1], 1150.0)

    # Day 3: New year, counter resets. Receive 10 * 80 = 800 dividend.
    # This is below the 1000 threshold for the new year. No tax.
    assert gross_div[3] == 800
    np.testing.assert_allclose(net_div[3], 800.0)


def test_reinvestment_vs_no_reinvestment_divergence():
    """
    Tests that wealth correctly diverges when reinvesting dividends vs. not,
    especially after a price change following a dividend payment.
    """
    n = 5
    # Price increases *after* the dividend is paid
    price = np.array([100.0, 100.0, 100.0, 110.0, 110.0])
    dividends = np.array([0.0, 0.0, 1.0, 0.0, 0.0])  # $1 dividend on day 2
    investment_amount = 1000.0
    # Invest only on the first day to establish a position
    investment_periods = np.array([True, False, False, False, False])
    yearly_periods = np.array([True, False, False, False, False])

    # --- Scenario 1: Reinvest Dividends ---
    (wealth_reinvest, _, stocks_reinvest, _, _) = _invest(
        price,
        dividends,
        investment_amount,
        investment_periods,
        yearly_periods,
        tax_rate=0.0,
        tax_free_return_threshold_per_annu=1000.0,
        reinvest_dividends=True,
    )

    # --- Scenario 2: Do NOT Reinvest Dividends ---
    (wealth_no_reinvest, _, stocks_no_reinvest, _, _) = _invest(
        price,
        dividends,
        investment_amount,
        investment_periods,
        yearly_periods,
        tax_rate=0.0,
        tax_free_return_threshold_per_annu=1000.0,
        reinvest_dividends=False,
    )

    # --- Assertions ---

    # On Day 0, both start with 10 stocks and wealth of 1000
    assert stocks_reinvest[0] == 10.0
    assert stocks_no_reinvest[0] == 10.0
    assert wealth_reinvest[0] == 1000.0
    assert wealth_no_reinvest[0] == 1000.0

    # On Day 2 (dividend day), the total wealth should be identical.
    # Reinvest: 10 (initial) + 0.1 (from div) = 10.1 stocks * $100 = $1010 wealth
    # No Reinvest: 10 stocks * $100 + $10 cash = $1010 wealth
    np.testing.assert_allclose(wealth_reinvest[2], 1010.0)
    np.testing.assert_allclose(wealth_no_reinvest[2], 1010.0)
    # But the number of stocks is different
    assert stocks_reinvest[2] == 10.1
    assert stocks_no_reinvest[2] == 10.0

    # On Day 3, the price increases to $110, and wealth should now diverge.
    # Reinvest: 10.1 stocks * $110 = $1111 wealth
    # No Reinvest: 10 stocks * $110 + $10 cash = $1110 wealth
    assert wealth_reinvest[3] == 1111.0
    assert wealth_no_reinvest[3] == 1110.0
    assert wealth_reinvest[3] > wealth_no_reinvest[3]

    # The final wealth should also be different
    assert wealth_reinvest[-1] > wealth_no_reinvest[-1]


def test_multi_year_scenario_with_taxes():
    """
    Tests a longer-term scenario spanning multiple years to verify
    tax-free threshold resets and the compounding effect of reinvestment.
    """
    # ... (all the setup code is the same as before) ...
    n = 30
    price = 100 + np.sin(np.arange(n) / 5) * 10
    investment_amount = 1000.0
    investment_periods = np.full(n, True)
    yearly_periods = np.array([i % 12 == 0 for i in range(n)])
    dividends = np.zeros(n)
    dividends[5] = 2.0
    dividends[10] = 3.0
    dividends[15] = 4.0
    tax_rate = 0.25
    tax_free_threshold = 100.0

    # --- Scenario 1: Reinvest Dividends ---
    (wealth_reinvest, _, stocks_reinvest, gross_div_re, net_div_re) = _invest(
        price,
        dividends,
        investment_amount,
        investment_periods,
        yearly_periods,
        tax_rate,
        tax_free_threshold,
        reinvest_dividends=True,
    )

    # --- Scenario 2: Do NOT Reinvest Dividends ---
    (wealth_no_reinvest, _, stocks_no_reinvest, gross_div_no, net_div_no) = _invest(
        price,
        dividends,
        investment_amount,
        investment_periods,
        yearly_periods,
        tax_rate,
        tax_free_threshold,
        reinvest_dividends=False,
    )

    # --- Assertions ---

    # (The tax assertions from before are still valid and good to keep)
    expected_tax_day_10 = (
        gross_div_re[5] + gross_div_re[10] - tax_free_threshold
    ) * tax_rate
    np.testing.assert_allclose(
        net_div_re[10], gross_div_re[10] - expected_tax_day_10, rtol=1e-5
    )
    expected_taxable_day_15 = gross_div_re[15] - tax_free_threshold
    np.testing.assert_allclose(
        net_div_re[15],
        gross_div_re[15] - (expected_taxable_day_15 * tax_rate),
        rtol=1e-5,
    )

    # --- Corrected Final State Assertions ---

    # 1. The reinvestment portfolio MUST have more shares. This tests the core mechanic.
    assert stocks_reinvest[-1] > stocks_no_reinvest[-1]

    # 2. (Optional) Check that the wealth calculation is consistent.
    # We don't assert one is greater, just that they are different, proving the logic branches worked.
    assert wealth_reinvest[-1] != wealth_no_reinvest[-1]
