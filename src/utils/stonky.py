"""Module for interacting with the Stonky LLM."""

from dataclasses import dataclass
import ollama


@dataclass
class StonkyGeneralInfo:
    """General information for the Stonky LLM."""

    investment_amount: int
    investment_kind: str
    years_ago: int

    def __str__(self):
        return f"""
[general]
investment_amount={self.investment_amount}
investment_kind={self.investment_kind}
years_ago={self.years_ago}
"""


@dataclass
class StonkySpecificInfo:
    """Specific information for the Stonky LLM."""

    name: str
    timing: str
    reinvest_dividends: bool

    def __str__(self):
        return f"""
[specific]
name={self.name}
timing={self.timing}
reinvest_dividends={self.reinvest_dividends}
"""


def build_prompt(
    strategies: list[StonkySpecificInfo], general_info: StonkyGeneralInfo
) -> str:
    """Build a prompt for the Stonky LLM.

    Args:
        strategies: List of StonkySpecificInfo objects.
        general_info: StonkyGeneralInfo object.

    Returns:
        str: The prompt.
    """
    prompt = str(general_info)
    for strategy in strategies:
        prompt += str(strategy)
    return prompt


class StonkyClient:
    """Client for interacting with the Stonky LLM."""

    def __init__(self, prompt: str, model: str = "stonky"):
        """Initialize the Stonky client.

        Args:
            prompt: The prompt to use for the LLM.
            model: The model to use for the LLM. Defaults to "stonky".
        """
        self.prompt = prompt
        self.model = model
        self.client = ollama.Client()

    def generate(self) -> str:
        """Generate a video description."""
        response = self.client.generate(model=self.model, prompt=self.prompt)
        return response.response
