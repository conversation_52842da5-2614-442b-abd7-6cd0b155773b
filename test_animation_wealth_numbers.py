"""Test for wealth number display in animations."""

import pandas as pd
import numpy as np
import tempfile
import os
from pathlib import Path
from src.animation.time_series_animation import create_wealth_animation
from src.utils.numbers import make_int_human_readable


def test_make_int_human_readable():
    """Test the make_int_human_readable function with various values."""
    assert make_int_human_readable(500) == "500"
    assert make_int_human_readable(1500) == "1.5k"
    assert make_int_human_readable(1000000) == "1.0mn"
    assert make_int_human_readable(2500000) == "2.5mn"
    assert make_int_human_readable(1000000000) == "1.0bn"


def test_create_wealth_animation_with_wealth_numbers():
    """Test that create_wealth_animation runs without errors with wealth numbers."""
    # Create sample data
    dates = pd.date_range(start="2020-01-01", end="2023-01-01", freq="M")
    n_periods = len(dates)

    # Create sample wealth data
    np.random.seed(42)  # For reproducible results

    # Simulate growing wealth over time
    base_investment = np.linspace(1000, 36000, n_periods)  # Growing investment

    # Simulate stock performance with some volatility
    aapl_multiplier = np.cumprod(1 + np.random.normal(0.01, 0.05, n_periods))
    msft_multiplier = np.cumprod(1 + np.random.normal(0.008, 0.04, n_periods))

    wealth_data = {
        "Total Investments": base_investment,
        "AAPL": base_investment * aapl_multiplier,
        "MSFT": base_investment * msft_multiplier,
    }

    wealth_df = pd.DataFrame(wealth_data, index=dates)

    # Create temporary file for animation
    with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
        temp_filename = tmp_file.name

    try:
        # Test that the function runs without errors
        create_wealth_animation(
            wealth_df=wealth_df,
            investment_years=3,
            filename=temp_filename,
            duration_sec=2,  # Short duration for testing
            fps=10,  # Low fps for faster testing
            title="Test Animation with Wealth Numbers",
            music_filename="DISABLED",  # Disable music for testing
        )

        # Check that the file was created
        assert os.path.exists(temp_filename)
        assert os.path.getsize(temp_filename) > 0

    finally:
        # Clean up
        if os.path.exists(temp_filename):
            os.unlink(temp_filename)


if __name__ == "__main__":
    test_make_int_human_readable()
    test_create_wealth_animation_with_wealth_numbers()
    print("All tests passed!")
